/* Detergent Theme Styles */
[data-theme="detergent"] {
    /* Detergent Theme Color Overrides */
    --text-dark: #0f172a;
    --text-light: #475569;
    --text-muted: #64748b;
    --white: #ffffff;
    --gray-50: #f0f9ff;
    --gray-100: #e0f2fe;
    --gray-200: #bae6fd;
    --gray-300: #7dd3fc;
    --gray-400: #38bdf8;
    --gray-500: #0ea5e9;
    --gray-600: #0284c7;
    --gray-700: #0369a1;
    --gray-800: #075985;
    --gray-900: #0c4a6e;

    /* Detergent theme specific colors */
    --bg-primary: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    --bg-secondary: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);
    --bg-tertiary: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 100%);
    --border-color: #bae6fd;
    --bubble-color: rgba(56, 189, 248, 0.3);
    --soap-color: rgba(240, 249, 255, 0.8);
    --clean-blue: #0ea5e9;
    --fresh-green: #10b981;
    --sparkle-color: #fbbf24;
}

[data-theme="detergent"] body {
    background: var(--bg-primary);
    color: var(--text-dark);
    position: relative;
    overflow-x: hidden;
}

[data-theme="detergent"] body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, var(--bubble-color) 2px, transparent 2px),
        radial-gradient(circle at 80% 20%, var(--bubble-color) 1px, transparent 1px),
        radial-gradient(circle at 40% 40%, var(--soap-color) 3px, transparent 3px);
    background-size: 200px 200px, 150px 150px, 300px 300px;
    animation: bubbleFloat 20s infinite linear;
    pointer-events: none;
    z-index: -1;
}

[data-theme="detergent"] .header {
    background: rgba(240, 249, 255, 0.95);
    border-bottom: 1px solid var(--border-color);
    backdrop-filter: blur(20px);
}

[data-theme="detergent"] .nav-menu a {
    color: var(--text-dark);
    transition: all 0.3s ease;
}

[data-theme="detergent"] .nav-menu a:hover {
    color: var(--clean-blue);
    text-shadow: 0 0 10px var(--bubble-color);
}

[data-theme="detergent"] .featured-categories {
    background: var(--bg-secondary);
}

[data-theme="detergent"] .category-card {
    background: var(--bg-secondary);
    border-color: var(--border-color);
    position: relative;
    overflow: hidden;
}

[data-theme="detergent"] .category-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, var(--soap-color) 0%, transparent 70%);
    transform: scale(0);
    transition: transform 0.6s ease;
}

[data-theme="detergent"] .category-card:hover::before {
    transform: scale(1);
    animation: cleaningWave 1s ease-out;
}

[data-theme="detergent"] .category-card:hover {
    border-color: var(--clean-blue);
    box-shadow: 0 10px 30px var(--bubble-color);
    transform: translateY(-5px);
}

[data-theme="detergent"] .features {
    background: var(--bg-primary);
}

[data-theme="detergent"] .feature-item {
    background: var(--bg-secondary);
    border-color: var(--border-color);
    position: relative;
}

[data-theme="detergent"] .feature-item:hover {
    animation: sparkle 0.8s ease-in-out;
}

[data-theme="detergent"] .lang-btn,
[data-theme="detergent"] .theme-btn {
    background: var(--bg-tertiary);
    color: var(--text-dark);
    border: 1px solid var(--border-color);
}

[data-theme="detergent"] .lang-btn:hover,
[data-theme="detergent"] .theme-btn:hover {
    background: var(--clean-blue);
    color: white;
    box-shadow: 0 5px 15px var(--bubble-color);
}

[data-theme="detergent"] .mobile-menu-toggle {
    color: var(--text-dark);
}

/* Detergent theme animations */
@keyframes bubbleFloat {
    0% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
    100% { transform: translateY(0px) rotate(360deg); }
}

@keyframes cleaningWave {
    0% { transform: scale(0) rotate(0deg); opacity: 1; }
    50% { transform: scale(0.5) rotate(180deg); opacity: 0.8; }
    100% { transform: scale(1) rotate(360deg); opacity: 0; }
}

@keyframes sparkle {
    0%, 100% { filter: brightness(1); }
    25% { filter: brightness(1.2) drop-shadow(0 0 10px var(--sparkle-color)); }
    50% { filter: brightness(1.4) drop-shadow(0 0 20px var(--sparkle-color)); }
    75% { filter: brightness(1.2) drop-shadow(0 0 10px var(--sparkle-color)); }
}

@keyframes soapBubble {
    0% { transform: translateY(100vh) scale(0); opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { transform: translateY(-100px) scale(1); opacity: 0; }
}

/* Detergent theme transitions */
[data-theme="detergent"] * {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Products Page Detergent Mode */
[data-theme="detergent"] .products-container {
    background: var(--bg-primary);
}

[data-theme="detergent"] .sidebar {
    background: var(--bg-secondary);
    border-color: var(--border-color);
}

[data-theme="detergent"] .sidebar-category {
    color: var(--text-light);
    border-color: var(--border-color);
}

[data-theme="detergent"] .sidebar-category:hover,
[data-theme="detergent"] .sidebar-category.active {
    background: var(--clean-blue);
    color: white;
    box-shadow: 0 5px 15px var(--bubble-color);
}

[data-theme="detergent"] .products-grid {
    background: var(--bg-secondary);
}

[data-theme="detergent"] .product-card {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    border-color: var(--border-color);
    box-shadow: 0 5px 15px var(--bubble-color);
}

[data-theme="detergent"] .product-card:hover {
    border-color: var(--clean-blue);
    transform: translateY(-5px);
    box-shadow: 0 10px 25px var(--bubble-color);
}

[data-theme="dark"] .product-benefits {
    background: linear-gradient(135deg, rgba(220, 38, 38, 0.1) 0%, rgba(220, 38, 38, 0.05) 100%);
}

[data-theme="dark"] .usage-instructions {
    background: linear-gradient(135deg, var(--bg-tertiary) 0%, rgba(75, 85, 99, 0.5) 100%);
}

[data-theme="dark"] .caution-storage {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(245, 158, 11, 0.05) 100%);
}

/* About Page Detergent Mode */
[data-theme="detergent"] .about-hero {
    background: linear-gradient(135deg, var(--clean-blue) 0%, var(--fresh-green) 100%);
    position: relative;
}

[data-theme="detergent"] .about-hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="white" opacity="0.3"><animate attributeName="r" values="2;4;2" dur="3s" repeatCount="indefinite"/></circle><circle cx="80" cy="30" r="1.5" fill="white" opacity="0.4"><animate attributeName="r" values="1.5;3;1.5" dur="2s" repeatCount="indefinite"/></circle><circle cx="50" cy="70" r="2.5" fill="white" opacity="0.2"><animate attributeName="r" values="2.5;5;2.5" dur="4s" repeatCount="indefinite"/></circle></svg>');
    pointer-events: none;
}

[data-theme="detergent"] .about-content {
    background: var(--bg-secondary);
}

[data-theme="detergent"] .timeline-item {
    background: var(--bg-secondary);
    border-color: var(--border-color);
}

[data-theme="detergent"] .team-member {
    background: var(--bg-secondary);
    border-color: var(--border-color);
    position: relative;
}

[data-theme="detergent"] .team-member:hover {
    animation: sparkle 0.8s ease-in-out;
}

/* Contact Page Detergent Mode */
[data-theme="detergent"] .contact-hero {
    background: linear-gradient(135deg, var(--clean-blue) 0%, var(--fresh-green) 100%);
    position: relative;
}

[data-theme="detergent"] .contact-hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="30" cy="40" r="1.5" fill="white" opacity="0.4"><animate attributeName="cy" values="40;20;40" dur="3s" repeatCount="indefinite"/></circle><circle cx="70" cy="60" r="2" fill="white" opacity="0.3"><animate attributeName="cy" values="60;40;60" dur="2.5s" repeatCount="indefinite"/></circle></svg>');
    pointer-events: none;
}

[data-theme="detergent"] .contact-form {
    background: var(--bg-secondary);
    border-color: var(--border-color);
    position: relative;
}

[data-theme="detergent"] .form-group input,
[data-theme="detergent"] .form-group textarea,
[data-theme="detergent"] .form-group select {
    background: var(--white);
    border-color: var(--border-color);
    color: var(--text-dark);
}

[data-theme="detergent"] .form-group input:focus,
[data-theme="detergent"] .form-group textarea:focus,
[data-theme="detergent"] .form-group select:focus {
    border-color: var(--clean-blue);
    background: var(--white);
    box-shadow: 0 0 0 3px var(--bubble-color);
}

[data-theme="detergent"] .contact-info-card {
    background: var(--bg-secondary);
    border-color: var(--border-color);
    position: relative;
}

[data-theme="detergent"] .contact-info-card:hover {
    animation: sparkle 0.8s ease-in-out;
}

/* Theme Toggle Icon */
[data-theme="detergent"] #theme-icon::before {
    content: "\f043"; /* fa-tint (droplet) */
}

[data-theme="light"] #theme-icon::before {
    content: "\f5d1"; /* fa-soap (soap bar) */
}

/* Floating Bubbles Effect */
[data-theme="detergent"] .bubble {
    position: fixed;
    border-radius: 50%;
    background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.8), var(--bubble-color));
    pointer-events: none;
    z-index: 1000;
    animation: soapBubble 8s infinite linear;
}

[data-theme="detergent"] .bubble:nth-child(1) { left: 10%; animation-delay: 0s; width: 20px; height: 20px; }
[data-theme="detergent"] .bubble:nth-child(2) { left: 20%; animation-delay: 2s; width: 15px; height: 15px; }
[data-theme="detergent"] .bubble:nth-child(3) { left: 30%; animation-delay: 4s; width: 25px; height: 25px; }
[data-theme="detergent"] .bubble:nth-child(4) { left: 40%; animation-delay: 1s; width: 18px; height: 18px; }
[data-theme="detergent"] .bubble:nth-child(5) { left: 50%; animation-delay: 3s; width: 22px; height: 22px; }
[data-theme="detergent"] .bubble:nth-child(6) { left: 60%; animation-delay: 5s; width: 16px; height: 16px; }
[data-theme="detergent"] .bubble:nth-child(7) { left: 70%; animation-delay: 1.5s; width: 24px; height: 24px; }
[data-theme="detergent"] .bubble:nth-child(8) { left: 80%; animation-delay: 3.5s; width: 19px; height: 19px; }
[data-theme="detergent"] .bubble:nth-child(9) { left: 90%; animation-delay: 2.5s; width: 21px; height: 21px; }

/* Button Enhancements */
[data-theme="detergent"] .btn-primary {
    background: linear-gradient(135deg, var(--clean-blue), var(--fresh-green));
    position: relative;
    overflow: hidden;
}

[data-theme="detergent"] .btn-primary::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    transform: scale(0);
    transition: transform 0.6s ease;
}

[data-theme="detergent"] .btn-primary:hover::before {
    transform: scale(1);
    animation: cleaningWave 1s ease-out;
}

[data-theme="detergent"] .btn-primary:hover {
    box-shadow: 0 10px 30px var(--bubble-color);
    transform: translateY(-2px);
}

/* Product Cards Enhancement */
[data-theme="detergent"] .product-card {
    position: relative;
    overflow: hidden;
}

[data-theme="detergent"] .product-card::after {
    content: '';
    position: absolute;
    top: -100%;
    left: -100%;
    width: 300%;
    height: 300%;
    background: conic-gradient(from 0deg, transparent, var(--soap-color), transparent);
    animation: rotate 4s linear infinite;
    opacity: 0;
    transition: opacity 0.3s ease;
}

[data-theme="detergent"] .product-card:hover::after {
    opacity: 1;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Footer Dark Mode */
[data-theme="dark"] .footer {
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
}

[data-theme="dark"] .footer-section h3 {
    color: var(--accent-red);
}

[data-theme="dark"] .footer-section p,
[data-theme="dark"] .footer-section a {
    color: var(--text-light);
}

[data-theme="dark"] .footer-section a:hover {
    color: var(--accent-red);
}

[data-theme="dark"] .social-links a {
    background: var(--bg-tertiary);
    color: var(--text-light);
}

[data-theme="dark"] .social-links a:hover {
    background: var(--primary-red);
    color: white;
}

[data-theme="dark"] .contact-info i {
    color: var(--accent-red);
}

[data-theme="dark"] .footer-bottom {
    border-top: 1px solid var(--border-color);
    color: var(--text-muted);
}

/* Smooth theme transition */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

